{"meta": {"title": "Text Case Converter - Modern Text Case Conversion Tool", "description": "Text Case Converter is a powerful text conversion tool that helps you transform text to uppercase, lowercase, title case and more. Supports multiple languages."}, "language": {"selectLanguage": "Language"}, "navigation": {"blog": "Blog", "textCaseConversion": "Text Case Converter", "specialFormatting": "Special Formatting", "codeAndData": "Code & Data", "translation": "Translation", "tools": "Tools", "settings": "Settings"}, "converter": {"title": "Free Text Case Converter", "introduction": "Easily convert your text to UPPER CASE, lower case, Capitalized Case, and more — instantly, with just one click!", "instructions": "Simply enter your text and choose the case you want to convert it to.", "placeholder": "Type or paste your content here...", "selectCase": "Select Case Type"}, "cases": {"sentence": "Sentence case", "lower": "lowercase", "upper": "UPPERCASE", "capitalized": "Capitalized Case", "alternating": "aLtErNaTiNg cAsE", "title": "Title Case", "inverse": "InVeRsE CaSe"}, "actions": {"convert": "Convert", "download": "Download", "copy": "Copy", "clear": "Clear", "support": "Support This Project"}, "stats": {"characterCount": "Character Count", "wordCount": "Word Count", "sentenceCount": "Sentence Count", "lineCount": "Line Count"}, "explanations": {"sectionTitle": "Text Case Converter Types", "sentence": {"title": "Sentence case", "description1": "The sentence case converter will allow you to paste any text you'd like, and it will automatically transform it to a fully formed structured sentence.", "description2": "It works by capitalizing the very first letter in each sentence, and will then go on to transform the rest of the text into lowercase as well as converting i's into I's.", "example": "This is an example of sentence case."}, "lower": {"title": "lowercase", "description": "If you are wondering how to uncapitalize text, this is exactly what the lower case text converter will allow you to do - it transforms all the letters in your text into lowercase letters.", "example": "this is an example of lower case."}, "upper": {"title": "UPPERCASE", "description": "The upper case transformer will take any text that you have and will generate all the letters into upper case ones. It will essentially make all lower case letters into CAPITALS.", "example": "THIS IS AN EXAMPLE OF UPPER CASE."}, "capitalized": {"title": "Capitalized Case", "description": "The capitalized case converter will automatically convert the starting letter of every word into an upper case and will leave the remaining letters as lower case ones.", "example": "This Is An Example Of Capitalized Case."}, "alternating": {"title": "aLtErNaTiNg cAsE", "description": "The alternating case converter will allow you to transform your text into text that alternates between lower case and upper case. It will generate a capital letter and then a lower case letter within the same word.", "example": "tHiS Is aN ExAmPlE Of aLtErNaTiNg cAsE."}, "title": {"title": "Title Case", "description": "The title case converter is perfect for those who are a bit unsure on how to title an upcoming essay. It ensures the correct letters are capitalized within the context of a title.", "example": "This Is an Example of Title Case."}}, "additionalTools": {"title": "Additional Text Tools", "viewAll": "View all text tools", "reverse": {"title": "Reverse Text Generator", "description": "Flip your text backwards"}, "upsideDown": {"title": "Upside Down Text", "description": "Flip your text upside down"}, "morseCode": {"title": "Morse Code Translator", "description": "Convert text to morse code"}, "smallText": {"title": "Small Text Generator", "description": "Create tiny stylized text"}, "strikethrough": {"title": "Strikethrough Text", "description": "Add a strike through your text"}, "wideText": {"title": "Wide Text Generator", "description": "Create aesthetically spaced text"}}, "faq": {"title": "Frequently Asked Questions", "why": {"question": "Why would I need to convert text case?", "answer": "Text case conversion is useful for formatting documents, preparing titles, fixing accidentally capitalized text, or maintaining consistent styling in your content."}, "secure": {"question": "Is my text data secure when using this tool?", "answer": "Absolutely. All text processing happens directly in your browser. We never store, transmit, or access the text you enter."}, "mobile": {"question": "Can I use this tool on my mobile device?", "answer": "Yes, our text case converter is fully responsive and works on all devices including smartphones, tablets, and desktop computers."}, "languages": {"question": "Does this tool work with non-English languages?", "answer": "Yes, our tool supports multiple languages and international character sets including accented characters, Cyrillic, Greek, and many others."}}, "footer": {"description": "A comprehensive collection of text transformation tools to help you format and optimize your content.", "popularTools": "Popular Tools", "resources": "Resources", "language": "Language", "allRightsReserved": "All rights reserved.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "contactUs": "Contact Us"}, "tools": {"textCaseConverter": "Text Case Converter", "loremIpsumGenerator": "Lorem Ipsum Generator", "characterCountTool": "Character Count <PERSON>", "wordCountTool": "Word Count Tool", "reverseTextGenerator": "Reverse Text Generator"}, "resources": {"blog": "Blog", "helpCenter": "Help Center", "apiDocumentation": "API Documentation", "suggestTool": "Suggest a Tool", "chromeExtension": "Chrome Extension"}, "toast": {"converted": "Text Converted", "convertedDescription": "Text has been converted successfully", "copied": "Text Copied", "copiedDescription": "Text has been copied to clipboard", "copyFailed": "<PERSON><PERSON> Failed", "copyFailedDescription": "Unable to copy text to clipboard", "downloaded": "Downloaded", "downloadedDescription": "Text has been downloaded as a file", "cleared": "Cleared", "clearedDescription": "Text has been cleared"}, "blog": {"title": "Blog", "subtitle": "Tips, guides, and insights about text conversion", "readMore": "Read More", "backToBlog": "Back to Blog", "publishedOn": "Published on", "readingTime": "min read", "noPostsFound": "No blog posts found.", "posts": {"textConversionTips": {"title": "5 Essential Text Conversion Tips for Productivity", "excerpt": "Discover powerful techniques to streamline your text formatting workflow and boost your productivity."}, "caseTypesGuide": {"title": "Complete Guide to Text Case Types", "excerpt": "Learn about all the different text case types and when to use each one effectively."}, "productivityHacks": {"title": "Text Formatting Productivity Hacks", "excerpt": "Speed up your workflow with these advanced text formatting techniques and shortcuts."}}}, "aboutUs": {"title": "About Us", "backToHome": "Back to Home", "mission": {"title": "Our Mission", "description": "Text Case Converter is dedicated to providing users with the simplest and most efficient text conversion tools. We believe that excellent tools should be free, easy to use, and truly improve user productivity."}, "features": {"title": "Our Features", "privacy": {"title": "Privacy Protection", "description": "All text processing is done locally in your browser, we never store or transmit your data."}, "multilingual": {"title": "Multilingual Support", "description": "Supports multiple languages and international character sets to meet the needs of global users."}, "responsive": {"title": "Responsive Design", "description": "Works perfectly on all devices including phones, tablets, and desktop computers."}, "free": {"title": "Completely Free", "description": "All features are free with no hidden fees or restrictions."}}, "contact": {"title": "Contact Us", "description": "If you have any questions, suggestions, or feedback, please feel free to contact us:", "email": "Email Address", "emailValue": "<EMAIL>"}}, "faqPage": {"title": "Frequently Asked Questions", "backToHome": "Back to Home", "subtitle": "Common questions and answers about Text Case Converter", "questions": {"whatIs": {"question": "What is Text Case Converter?", "answer": "Text Case Converter is a free online text conversion tool that helps you convert text to different case formats like uppercase, lowercase, title case, and more. All processing is done locally in your browser."}, "howToUse": {"question": "How do I use this tool?", "answer": "It's very simple: 1) Type or paste your text in the text box; 2) Select the case type you want; 3) Click the convert button; 4) Copy or download the converted text."}, "supportedLanguages": {"question": "What languages are supported?", "answer": "Our tool supports multiple languages including English, Chinese, Spanish, French, German, and various international character sets like accented characters, Cyrillic, Greek, and many others."}, "dataPrivacy": {"question": "Is my data safe?", "answer": "Absolutely. All text processing happens directly in your browser, we never store, transmit, or access the text you enter. Your privacy is our top priority."}, "mobileSupport": {"question": "Can I use this on mobile?", "answer": "Yes, our tool is fully responsive and works perfectly on all devices including smartphones, tablets, and desktop computers."}, "cost": {"question": "Do I need to pay to use this tool?", "answer": "No. Text Case Converter is completely free with no hidden fees or usage restrictions. We are committed to providing free text conversion services to all users."}, "textLimits": {"question": "Are there text length limits?", "answer": "There are no strict text length limits. However, for optimal performance, we recommend not processing extremely large texts. For large documents, you can process them in sections."}, "browserSupport": {"question": "Which browsers are supported?", "answer": "Our tool supports all modern browsers including Chrome, Firefox, Safari, Edge, and more. We recommend using the latest version of your browser for the best experience."}}}, "footerNew": {"support": {"title": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us"}, "legal": {"title": "Legal", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy"}, "about": {"title": "About", "aboutUs": "About Us", "faq": "FAQ"}}}