import fs from 'fs';
import path from 'path';

// 动态导入博客文章数据
async function getBlogPosts() {
  try {
    const blogDataPath = path.join(process.cwd(), 'client/src/data/blog-posts.ts');
    const blogData = fs.readFileSync(blogDataPath, 'utf-8');
    
    const posts = [];
    const slugMatches = blogData.match(/slug:\s*["']([^"']+)["']/g);
    const dateMatches = blogData.match(/publishedAt:\s*["']([^"']+)["']/g);
    
    if (slugMatches && dateMatches) {
      for (let i = 0; i < Math.min(slugMatches.length, dateMatches.length); i++) {
        const slug = slugMatches[i].match(/["']([^"']+)["']/)[1];
        const date = dateMatches[i].match(/["']([^"']+)["']/)[1];
        
        posts.push({
          slug,
          lastmod: date,
          changefreq: 'monthly',
          priority: '0.7'
        });
      }
    }
    
    return posts;
  } catch (error) {
    console.warn('⚠️ 无法读取博客文章数据，使用默认数据:', error.message);
    return [
      {
        slug: 'text-conversion-tips',
        lastmod: '2024-12-15',
        changefreq: 'monthly',
        priority: '0.7'
      }
    ];
  }
}

// 网站配置
const config = {
  siteUrl: 'https://textcase.top',
  outputDir: './dist/public',
  clientPublicDir: './client/public',
  lastmod: new Date().toISOString().split('T')[0],
  languages: ['en', 'es', 'fr', 'de', 'zh'],
  pages: [
    {
      path: '/',
      changefreq: 'weekly',
      priority: '1.0',
      title: 'Homepage'
    },
    {
      path: '/blog',
      changefreq: 'weekly',
      priority: '0.9',
      title: 'Blog'
    },
    {
      path: '/privacy-policy',
      changefreq: 'monthly',
      priority: '0.8',
      title: 'Privacy Policy'
    },
    {
      path: '/terms-of-service',
      changefreq: 'monthly',
      priority: '0.8',
      title: 'Terms of Service'
    }
  ],
  excludePaths: ['/404', '/api/', '/test-blog'],
};

// 确保输出目录存在
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// 生成hreflang链接（超紧凑格式）
function generateHrefLangLinks(basePath, languages, siteUrl) {
  const links = languages.map(lang => {
    const langPath = lang === 'en' ? '' : `/${lang}`;
    const href = `${siteUrl}${langPath}${basePath}`;
    return `<xhtml:link rel="alternate" hreflang="${lang}" href="${href}" />`;
  }).join(' ');

  const defaultLink = `<xhtml:link rel="alternate" hreflang="x-default" href="${siteUrl}${basePath}" />`;

  return `    ${links} ${defaultLink}`;
}

// 生成单个URL条目（紧凑格式）
function generateUrlEntry(url, lastmod, changefreq, priority, basePath, languages, siteUrl) {
  const hrefLangLinks = generateHrefLangLinks(basePath, languages, siteUrl);

  return `  <url>
    <loc>${url}</loc><lastmod>${lastmod}</lastmod><changefreq>${changefreq}</changefreq><priority>${priority}</priority>
${hrefLangLinks}
  </url>`;
}

// 生成网站地图
async function generateSitemap() {
  const { siteUrl, outputDir, pages, languages, lastmod } = config;
  const blogPosts = await getBlogPosts();

  ensureDirectoryExists(outputDir);

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">

`;

  // 统计信息
  let urlCount = 0;

  // 1. 主页面（按优先级排序）
  sitemap += `  <!-- ========== 主要页面 ========== -->\n`;
  
  pages.forEach(page => {
    languages.forEach(lang => {
      const langPath = lang === 'en' ? '' : `/${lang}`;
      const pagePath = page.path === '/' ? '' : page.path;
      const fullPath = `${langPath}${pagePath}`;
      const fullUrl = `${siteUrl}${fullPath}`;
      const adjustedPriority = lang === 'en' ? page.priority : (parseFloat(page.priority) * 0.9).toFixed(1);
      
      sitemap += generateUrlEntry(
        fullUrl,
        lastmod,
        page.changefreq,
        adjustedPriority,
        page.path === '/' ? '' : page.path,
        languages,
        siteUrl
      ) + '\n\n';
      
      urlCount++;
    });
  });

  // 2. 博客文章
  if (blogPosts.length > 0) {
    sitemap += `  <!-- ========== 博客文章 ========== -->\n`;
    
    blogPosts.forEach(post => {
      languages.forEach(lang => {
        const langPath = lang === 'en' ? '' : `/${lang}`;
        const postPath = `/blog/${post.slug}`;
        const fullPath = `${langPath}${postPath}`;
        const fullUrl = `${siteUrl}${fullPath}`;
        const adjustedPriority = lang === 'en' ? post.priority : (parseFloat(post.priority) * 0.9).toFixed(1);
        
        sitemap += generateUrlEntry(
          fullUrl,
          post.lastmod,
          post.changefreq,
          adjustedPriority,
          postPath,
          languages,
          siteUrl
        ) + '\n\n';
        
        urlCount++;
      });
    });
  }

  sitemap += `</urlset>`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'sitemap.xml'), sitemap);
  console.log(`✅ 优化版网站地图已生成: ${path.join(outputDir, 'sitemap.xml')}`);
  console.log(`📊 包含 ${urlCount} 个URL条目`);
  console.log(`📄 ${pages.length} 个页面 × ${languages.length} 种语言 = ${pages.length * languages.length} 个页面URL`);
  console.log(`📝 ${blogPosts.length} 篇博客 × ${languages.length} 种语言 = ${blogPosts.length * languages.length} 个博客URL`);

  // 同时复制到客户端公共目录（用于开发环境）
  fs.writeFileSync(path.join(config.clientPublicDir, 'sitemap.xml'), sitemap);
  console.log(`✅ 网站地图已复制到客户端目录: ${path.join(config.clientPublicDir, 'sitemap.xml')}`);
  
  return { urlCount, pages: pages.length, blogPosts: blogPosts.length, languages: languages.length };
}

// 生成 robots.txt（优化版）
function generateRobotsTxt() {
  const { siteUrl, outputDir, excludePaths } = config;

  ensureDirectoryExists(outputDir);

  let robotsTxt = `# Robots.txt for ${siteUrl}
# Generated on ${new Date().toISOString()}

# 允许所有搜索引擎爬虫访问
User-agent: *
Allow: /

# 禁止访问的路径
`;

  // 添加禁止访问的路径
  excludePaths.forEach(path => {
    if (path.startsWith('/')) {
      robotsTxt += `Disallow: ${path}\n`;
    }
  });

  robotsTxt += `
# 特定搜索引擎优化
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# 禁止一些常见的爬虫
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

# 网站地图链接
Sitemap: ${siteUrl}/sitemap.xml

# 主机信息
Host: ${siteUrl.replace('https://', '').replace('http://', '')}
`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已生成: ${path.join(outputDir, 'robots.txt')}`);

  // 同时复制到客户端公共目录（用于开发环境）
  fs.writeFileSync(path.join(config.clientPublicDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已复制到客户端目录: ${path.join(config.clientPublicDir, 'robots.txt')}`);
}

// 生成sitemap索引文件（为将来扩展准备）
function generateSitemapIndex() {
  const { siteUrl, outputDir } = config;

  const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${siteUrl}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`;

  fs.writeFileSync(path.join(outputDir, 'sitemap-index.xml'), sitemapIndex);
  fs.writeFileSync(path.join(config.clientPublicDir, 'sitemap-index.xml'), sitemapIndex);
  console.log(`✅ sitemap索引文件已生成`);
}

// 主执行函数
async function main() {
  try {
    console.log('🚀 开始生成优化版网站地图...\n');

    const stats = await generateSitemap();
    generateRobotsTxt();
    generateSitemapIndex();

    console.log('\n📈 生成统计:');
    console.log(`   总URL数量: ${stats.urlCount}`);
    console.log(`   页面数量: ${stats.pages}`);
    console.log(`   博客文章: ${stats.blogPosts}`);
    console.log(`   支持语言: ${stats.languages}`);
    console.log(`   文件大小优化: 原文件495行 → 新文件约${Math.ceil(stats.urlCount * 8 + 20)}行`);

    console.log('\n✅ 优化版网站地图和相关文件生成完成！');
    console.log('\n🎯 优化效果:');
    console.log('   ✓ 更清晰的分组结构');
    console.log('   ✓ 紧凑的hreflang格式');
    console.log('   ✓ 减少重复内容');
    console.log('   ✓ 提高可读性');
    console.log('   ✓ 保持SEO效果');

  } catch (error) {
    console.error('❌ 生成过程中出现错误:', error);
    process.exit(1);
  }
}

main();
