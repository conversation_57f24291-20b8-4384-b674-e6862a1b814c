{"meta": {"title": "Text Case Converter - 现代文本大小写转换工具", "description": "Text Case Converter 是一个强大的文本转换工具，可帮助您将文本转换为大写、小写、标题大小写等。支持多种语言。"}, "language": {"selectLanguage": "语言"}, "navigation": {"blog": "博客", "textCaseConversion": "文本大小写转换", "specialFormatting": "特殊格式", "codeAndData": "代码和数据", "translation": "翻译", "tools": "工具", "settings": "设置"}, "converter": {"title": "免费文本大小写转换器", "introduction": "轻松将您的文本转换为大写、小写、首字母大写等格式——只需一键即可即时完成！", "instructions": "只需输入您的文本并选择要转换的格式即可。", "placeholder": "在此处键入或粘贴您的内容...", "selectCase": "选择大小写类型"}, "cases": {"sentence": "句子大小写", "lower": "小写", "upper": "大写", "capitalized": "首字母大写", "alternating": "交替大小写", "title": "标题大小写", "inverse": "反转大小写"}, "actions": {"convert": "转换", "download": "下载", "copy": "复制", "clear": "清除", "support": "支持此项目"}, "stats": {"characterCount": "字符数", "wordCount": "单词数", "sentenceCount": "句子数", "lineCount": "行数"}, "explanations": {"sectionTitle": "文本大小写转换类型", "sentence": {"title": "句子大小写", "description1": "句子大小写转换器允许您粘贴任何文本，它将自动将其转换为完整的结构化句子。", "description2": "它通过将每个句子的第一个字母大写，然后将其余文本转换为小写以及将 i 转换为 I 来工作。", "example": "这是句子大小写的示例。"}, "lower": {"title": "小写", "description": "如果您想知道如何取消文本大写，这正是小写文本转换器允许您做的 - 它将文本中的所有字母转换为小写字母。", "example": "这是小写的示例。"}, "upper": {"title": "大写", "description": "大写转换器将获取您拥有的任何文本，并将所有字母生成为大写字母。它基本上会将所有小写字母转换为大写字母。", "example": "这是大写的示例。"}, "capitalized": {"title": "首字母大写", "description": "首字母大写转换器将自动将每个单词的首字母转换为大写，并将其余字母保留为小写。", "example": "这是首字母大写的示例。"}, "alternating": {"title": "交替大小写", "description": "交替大小写转换器允许您将文本转换为在小写和大写之间交替的文本。它将在同一个单词中生成一个大写字母，然后是一个小写字母。", "example": "这是交替大小写的示例。"}, "title": {"title": "标题大小写", "description": "标题大小写转换器对于那些不确定如何为即将到来的文章命名标题的人来说是完美的。它确保在标题的上下文中正确大写字母。", "example": "这是标题大小写的示例。"}}, "additionalTools": {"title": "其他文本工具", "viewAll": "查看所有文本工具", "reverse": {"title": "文本反转生成器", "description": "将文本反向翻转"}, "upsideDown": {"title": "上下颠倒文本", "description": "将文本上下颠倒"}, "morseCode": {"title": "摩尔斯电码转换器", "description": "将文本转换为摩尔斯电码"}, "smallText": {"title": "小文本生成器", "description": "创建微型样式文本"}, "strikethrough": {"title": "删除线文本", "description": "为文本添加删除线"}, "wideText": {"title": "宽文本生成器", "description": "创建美观间隔的文本"}}, "faq": {"title": "常见问题", "why": {"question": "为什么我需要转换文本大小写？", "answer": "文本大小写转换对于格式化文档、准备标题、修复意外大写的文本或在内容中保持一致的样式非常有用。"}, "secure": {"question": "使用此工具时，我的文本数据安全吗？", "answer": "绝对安全。所有文本处理都直接在您的浏览器中进行。我们从不存储、传输或访问您输入的文本。"}, "mobile": {"question": "我可以在移动设备上使用此工具吗？", "answer": "是的，我们的文本大小写转换器完全响应式，可在包括智能手机、平板电脑和台式计算机在内的所有设备上使用。"}, "languages": {"question": "此工具适用于非英语语言吗？", "answer": "是的，我们的工具支持多种语言和国际字符集，包括重音字符、西里尔字母、希腊字母等。"}}, "footer": {"description": "一个全面的文本转换工具集合，帮助您格式化和优化您的内容。", "popularTools": "热门工具", "resources": "资源", "language": "语言", "allRightsReserved": "版权所有。", "privacyPolicy": "隐私政策", "termsOfService": "服务条款", "contactUs": "联系我们"}, "tools": {"textCaseConverter": "文本大小写转换器", "loremIpsumGenerator": "<PERSON><PERSON>um 生成器", "characterCountTool": "字符计数工具", "wordCountTool": "单词计数工具", "reverseTextGenerator": "文本反转生成器"}, "resources": {"blog": "博客", "helpCenter": "帮助中心", "apiDocumentation": "API 文档", "suggestTool": "建议工具", "chromeExtension": "Chrome 扩展"}, "toast": {"converted": "文本已转换", "convertedDescription": "文本已成功转换", "copied": "已复制文本", "copiedDescription": "文本已复制到剪贴板", "copyFailed": "复制失败", "copyFailedDescription": "无法将文本复制到剪贴板", "downloaded": "已下载", "downloadedDescription": "文本已作为文件下载", "cleared": "已清除", "clearedDescription": "文本已被清除"}, "blog": {"title": "博客", "subtitle": "关于文本转换的技巧、指南和见解", "readMore": "阅读更多", "backToBlog": "返回博客", "publishedOn": "发布于", "readingTime": "分钟阅读", "noPostsFound": "未找到博客文章。", "posts": {"textConversionTips": {"title": "提高生产力的5个文本转换技巧", "excerpt": "发现强大的技术来简化您的文本格式化工作流程并提高生产力。"}, "caseTypesGuide": {"title": "文本大小写类型完整指南", "excerpt": "了解所有不同的文本大小写类型以及何时有效使用每种类型。"}, "productivityHacks": {"title": "文本格式化生产力技巧", "excerpt": "通过这些高级文本格式化技术和快捷方式加快您的工作流程。"}}}, "aboutUs": {"title": "关于我们", "backToHome": "返回首页", "mission": {"title": "我们的使命", "description": "Text Case Converter 致力于为用户提供最简单、最高效的文本转换工具。我们相信优秀的工具应该是免费的、易用的，并且能够真正提高用户的工作效率。"}, "features": {"title": "我们的特色", "privacy": {"title": "隐私保护", "description": "所有文本处理都在您的浏览器中本地完成，我们从不存储或传输您的数据。"}, "multilingual": {"title": "多语言支持", "description": "支持多种语言和国际字符集，满足全球用户的需求。"}, "responsive": {"title": "响应式设计", "description": "在所有设备上都能完美运行，包括手机、平板和桌面电脑。"}, "free": {"title": "完全免费", "description": "所有功能都是免费的，没有隐藏费用或限制。"}}, "contact": {"title": "联系我们", "description": "如果您有任何问题、建议或反馈，请随时与我们联系：", "email": "邮箱地址", "emailValue": "<EMAIL>"}}, "faqPage": {"title": "常见问题", "backToHome": "返回首页", "subtitle": "关于 Text Case Converter 的常见问题解答", "questions": {"whatIs": {"question": "什么是 Text Case Converter？", "answer": "Text Case Converter 是一个免费的在线文本转换工具，可以帮助您将文本转换为不同的大小写格式，如大写、小写、标题大小写等。所有处理都在您的浏览器中本地完成。"}, "howToUse": {"question": "如何使用这个工具？", "answer": "使用非常简单：1) 在文本框中输入或粘贴您的文本；2) 选择您想要的大小写类型；3) 点击转换按钮；4) 复制或下载转换后的文本。"}, "supportedLanguages": {"question": "支持哪些语言？", "answer": "我们的工具支持多种语言，包括英语、中文、西班牙语、法语、德语等，以及各种国际字符集，如重音字符、西里尔字母、希腊字母等。"}, "dataPrivacy": {"question": "我的数据安全吗？", "answer": "绝对安全。所有文本处理都直接在您的浏览器中进行，我们从不存储、传输或访问您输入的文本。您的隐私是我们的首要关注。"}, "mobileSupport": {"question": "可以在手机上使用吗？", "answer": "是的，我们的工具完全响应式，可以在包括智能手机、平板电脑和桌面电脑在内的所有设备上完美运行。"}, "cost": {"question": "使用这个工具需要付费吗？", "answer": "不需要。Text Case Converter 完全免费，没有任何隐藏费用或使用限制。我们致力于为所有用户提供免费的文本转换服务。"}, "textLimits": {"question": "有文本长度限制吗？", "answer": "没有严格的文本长度限制。但是，为了确保最佳性能，我们建议处理的文本不要过于庞大。对于大型文档，您可以分段处理。"}, "browserSupport": {"question": "支持哪些浏览器？", "answer": "我们的工具支持所有现代浏览器，包括 Chrome、Firefox、Safari、Edge 等。建议使用最新版本的浏览器以获得最佳体验。"}}}, "footerNew": {"support": {"title": "支持", "helpCenter": "帮助中心", "contactUs": "联系我们"}, "legal": {"title": "法律", "termsOfService": "服务条款", "privacyPolicy": "隐私政策"}, "about": {"title": "关于", "aboutUs": "关于我们", "faq": "常见问题"}}}