import fs from 'fs';
import path from 'path';

// 动态导入博客文章数据
async function getBlogPosts() {
  try {
    const blogDataPath = path.join(process.cwd(), 'client/src/data/blog-posts.ts');
    const blogData = fs.readFileSync(blogDataPath, 'utf-8');
    
    const posts = [];
    const slugMatches = blogData.match(/slug:\s*["']([^"']+)["']/g);
    const dateMatches = blogData.match(/publishedAt:\s*["']([^"']+)["']/g);
    
    if (slugMatches && dateMatches) {
      for (let i = 0; i < Math.min(slugMatches.length, dateMatches.length); i++) {
        const slug = slugMatches[i].match(/["']([^"']+)["']/)[1];
        const date = dateMatches[i].match(/["']([^"']+)["']/)[1];
        
        posts.push({
          slug,
          lastmod: date,
          changefreq: 'monthly',
          priority: '0.7'
        });
      }
    }
    
    return posts;
  } catch (error) {
    console.warn('⚠️ 无法读取博客文章数据，使用默认数据:', error.message);
    return [
      {
        slug: 'text-conversion-tips',
        lastmod: '2024-12-15',
        changefreq: 'monthly',
        priority: '0.7'
      }
    ];
  }
}

// 网站配置
const config = {
  siteUrl: 'https://textcase.top',
  outputDir: './dist/public',
  clientPublicDir: './client/public',
  lastmod: new Date().toISOString().split('T')[0],
  languages: ['en', 'es', 'fr', 'de', 'zh'],
  pages: [
    { path: '/', changefreq: 'weekly', priority: '1.0', title: 'Homepage' },
    { path: '/blog', changefreq: 'weekly', priority: '0.9', title: 'Blog' },
    { path: '/privacy-policy', changefreq: 'monthly', priority: '0.8', title: 'Privacy Policy' },
    { path: '/terms-of-service', changefreq: 'monthly', priority: '0.8', title: 'Terms of Service' }
  ],
  excludePaths: ['/404', '/api/', '/test-blog'],
};

// 确保输出目录存在
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// 生成紧凑的URL条目
function generateCompactUrlEntry(url, lastmod, changefreq, priority, basePath, languages, siteUrl) {
  // 生成hreflang链接（一行格式）
  const hrefLangLinks = languages.map(lang => {
    const langPath = lang === 'en' ? '' : `/${lang}`;
    const href = `${siteUrl}${langPath}${basePath}`;
    return `<xhtml:link rel="alternate" hreflang="${lang}" href="${href}" />`;
  }).join(' ');
  
  const defaultLink = `<xhtml:link rel="alternate" hreflang="x-default" href="${siteUrl}${basePath}" />`;
  
  return `  <url>
    <loc>${url}</loc><lastmod>${lastmod}</lastmod><changefreq>${changefreq}</changefreq><priority>${priority}</priority>
    ${hrefLangLinks} ${defaultLink}
  </url>`;
}

// 生成网站地图
async function generateSitemap() {
  const { siteUrl, outputDir, pages, languages, lastmod } = config;
  const blogPosts = await getBlogPosts();

  ensureDirectoryExists(outputDir);

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">

  <!-- ========== 主要页面 (${pages.length} pages × ${languages.length} languages = ${pages.length * languages.length} URLs) ========== -->
`;

  let urlCount = 0;

  // 1. 主页面
  pages.forEach(page => {
    languages.forEach(lang => {
      const langPath = lang === 'en' ? '' : `/${lang}`;
      const pagePath = page.path === '/' ? '' : page.path;
      const fullPath = `${langPath}${pagePath}`;
      const fullUrl = `${siteUrl}${fullPath}`;
      const adjustedPriority = lang === 'en' ? page.priority : (parseFloat(page.priority) * 0.9).toFixed(1);
      
      sitemap += generateCompactUrlEntry(
        fullUrl,
        lastmod,
        page.changefreq,
        adjustedPriority,
        page.path === '/' ? '' : page.path,
        languages,
        siteUrl
      ) + '\n\n';
      
      urlCount++;
    });
  });

  // 2. 博客文章
  if (blogPosts.length > 0) {
    sitemap += `  <!-- ========== 博客文章 (${blogPosts.length} posts × ${languages.length} languages = ${blogPosts.length * languages.length} URLs) ========== -->
`;
    
    blogPosts.forEach(post => {
      languages.forEach(lang => {
        const langPath = lang === 'en' ? '' : `/${lang}`;
        const postPath = `/blog/${post.slug}`;
        const fullPath = `${langPath}${postPath}`;
        const fullUrl = `${siteUrl}${fullPath}`;
        const adjustedPriority = lang === 'en' ? post.priority : (parseFloat(post.priority) * 0.9).toFixed(1);
        
        sitemap += generateCompactUrlEntry(
          fullUrl,
          post.lastmod,
          post.changefreq,
          adjustedPriority,
          postPath,
          languages,
          siteUrl
        ) + '\n\n';
        
        urlCount++;
      });
    });
  }

  sitemap += `</urlset>`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'sitemap.xml'), sitemap);
  fs.writeFileSync(path.join(config.clientPublicDir, 'sitemap.xml'), sitemap);
  
  console.log(`✅ 清洁版网站地图已生成并复制到两个位置`);
  console.log(`📊 总计 ${urlCount} 个URL条目`);
  console.log(`📄 ${pages.length} 页面 × ${languages.length} 语言 = ${pages.length * languages.length} 页面URL`);
  console.log(`📝 ${blogPosts.length} 博客 × ${languages.length} 语言 = ${blogPosts.length * languages.length} 博客URL`);
  
  // 计算文件行数
  const lineCount = sitemap.split('\n').length;
  console.log(`📏 文件大小: ${lineCount} 行 (原版: 495行, 优化: ${Math.round((1 - lineCount/495) * 100)}%)`);
  
  return { urlCount, pages: pages.length, blogPosts: blogPosts.length, languages: languages.length, lineCount };
}

// 生成 robots.txt
function generateRobotsTxt() {
  const { siteUrl, outputDir, excludePaths } = config;

  ensureDirectoryExists(outputDir);

  let robotsTxt = `# Robots.txt for ${siteUrl}
# Generated on ${new Date().toISOString()}

User-agent: *
Allow: /

# 禁止访问的路径
`;

  excludePaths.forEach(path => {
    if (path.startsWith('/')) {
      robotsTxt += `Disallow: ${path}\n`;
    }
  });

  robotsTxt += `
# 搜索引擎优化
User-agent: Googlebot
Crawl-delay: 1

User-agent: Bingbot
Crawl-delay: 1

# 网站地图
Sitemap: ${siteUrl}/sitemap.xml

# 主机
Host: ${siteUrl.replace('https://', '').replace('http://', '')}
`;

  fs.writeFileSync(path.join(outputDir, 'robots.txt'), robotsTxt);
  fs.writeFileSync(path.join(config.clientPublicDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已生成并复制`);
}

// 主执行函数
async function main() {
  try {
    console.log('🚀 生成清洁优化版网站地图...\n');
    
    const stats = await generateSitemap();
    generateRobotsTxt();
    
    console.log('\n🎯 优化效果:');
    console.log('   ✓ 紧凑的XML格式');
    console.log('   ✓ 清晰的分组注释');
    console.log('   ✓ 一行式hreflang链接');
    console.log('   ✓ 减少文件大小');
    console.log('   ✓ 保持SEO完整性');
    console.log('\n✅ 完成！');
    
  } catch (error) {
    console.error('❌ 错误:', error);
    process.exit(1);
  }
}

main();
